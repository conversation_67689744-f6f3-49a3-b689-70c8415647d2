# 🤖 PROMPT COMPLET POUR AGENT AI - SOTRAMINE PHOSPHATE

## 📋 PROMPT EXACT À UTILISER AVEC UN AGENT AI

---

**Copie ce prompt exactement et colle-le à un agent AI de développement :**

---

```
Je veux que tu développes une application complète de gestion de maintenance industrielle pour SOTRAMINE PHOSPHATE. Voici les spécifications détaillées :

## 🏭 CONTEXTE
- **Entreprise :** SOTRAMINE PHOSPHATE (industrie minière - extraction phosphate)
- **Application :** Système complet de gestion de maintenance industrielle
- **Technologie :** Python + PyQt5
- **Interface :** Moderne, professionnelle, responsive
- **Utilisateurs :** Techniciens, superviseurs, managers industriels

## 🎯 OBJECTIF
Créer une application desktop complète avec interface moderne pour gérer :
- Équipements industriels (broyeurs, convoyeurs, pompes, moteurs)
- Tâches de maintenance préventive/corrective
- Personnel et pointage
- Pièces de rechange et stocks
- Rapports et analyses
- Tableaux de bord avec KPIs temps réel

## 🎨 DESIGN REQUIS
**Palette de couleurs :**
- Primaire : Bleu industriel (#1e3a8a, #3b82f6)
- Secondaire : Gris foncé (#1f2937, #374151)
- Succès : Vert (#10b981)
- Attention : Orange (#f59e0b)
- Danger : Rouge (#ef4444)
- Info : Cyan (#06b6d4)

**Layout :**
```
┌─────────────────────────────────────────────────────────┐
│                    BARRE DE TITRE                       │
├──────────────┬──────────────────────────────────────────┤
│   SIDEBAR    │           ZONE DE CONTENU                │
│   (350px)    │              PRINCIPALE                  │
│              │                                          │
│   - Logo     │   ┌─────────────────────────────────┐    │
│   - Menu     │   │         MODULE ACTUEL           │    │
│   - Sections │   │                                 │    │
│   - Footer   │   │    (Tableau de bord, Tâches,   │    │
│              │   │     Équipements, etc.)         │    │
│              │   │                                 │    │
│              │   └─────────────────────────────────┘    │
├──────────────┴──────────────────────────────────────────┤
│                  BARRE DE STATUT                        │
└─────────────────────────────────────────────────────────┘
```

## 📊 MODULES REQUIS (par ordre de priorité)

### 1. 🏠 TABLEAU DE BORD PRINCIPAL
- **KPIs temps réel :** 8 indicateurs avec couleurs (Production 94.2%, Maintenance 89.7%, Personnel 96.8%, Efficacité 91.3%, Qualité 98.1%, Coûts €142K, Interventions 23, Stock critique 5)
- **Graphiques :** Courbes de production, barres de maintenance
- **Alertes :** 4 niveaux colorés (Critique/Rouge, Important/Orange, Info/Vert, Rappel/Bleu)
- **Actions rapides :** 6 boutons colorés vers modules principaux
- **Heure temps réel :** Mise à jour chaque seconde

### 2. 📋 GESTION DES TÂCHES COMPLÈTE
- **Table des tâches :** ID, Titre, Statut, Priorité, Assigné, Dates
- **Formulaire création :** Tous les champs (équipement, technicien, dates, instructions)
- **Statuts :** En attente, En cours, Terminée, Annulée, En pause
- **Priorités :** Critique, Haute, Normale, Faible
- **Filtres et recherche :** Par statut, priorité, technicien, équipement
- **Données d'exemple :** 20-25 tâches réalistes

### 3. 🔌 GESTION DES ÉQUIPEMENTS
- **Fiches techniques :** Code, nom, type, marque, modèle, localisation
- **États :** Opérationnel, En maintenance, Arrêté, Réformé
- **Historique :** Interventions, pannes, coûts
- **Maintenance :** Planning préventif, alertes
- **Données d'exemple :** 15-20 équipements industriels

### 4. 👥 GESTION DU PERSONNEL
- **Base de données :** Nom, poste, département, compétences
- **Pointage :** Présences quotidiennes avec statuts
- **Planning :** Horaires, congés, formations
- **Données d'exemple :** 12-15 personnes

### 5. 🔧 PIÈCES DE RECHANGE
- **Inventaire :** Code, nom, stock, seuil d'alerte, localisation
- **Mouvements :** Entrées, sorties, transferts
- **Alertes stock :** Notifications automatiques
- **Données d'exemple :** 30-40 pièces

### 6. 🛠️ CENTRE DE MAINTENANCE
- **Interventions :** Bons de travail, planning, ressources
- **Procédures :** Guides step-by-step
- **Rapports :** Comptes-rendus détaillés

### 7. 📊 RAPPORTS ET ANALYSES
- **Types :** Production, maintenance, personnel, coûts
- **Export :** PDF, Excel, CSV
- **Graphiques :** Courbes, barres, camemberts

### 8. ⚙️ PARAMÈTRES SYSTÈME
- **Configuration :** Utilisateurs, notifications, sauvegarde
- **Import/Export :** Données, configurations

## 💾 BASE DE DONNÉES
**SQLite avec tables :**
- equipments (id, code, name, type, brand, model, location, status, specs)
- tasks (id, title, description, type, priority, status, equipment_id, assigned_to, dates, hours, cost)
- personnel (id, employee_id, name, position, department, skills, status)
- spare_parts (id, code, name, category, stock, min_stock, location, cost)
- interventions (id, work_order, equipment_id, technician_id, type, dates, description, cost)
- attendance (id, employee_id, date, check_in, check_out, hours, status)

## 📋 DONNÉES D'EXEMPLE RÉALISTES

**ÉQUIPEMENTS :**
- BROYEUR-001 : Broyeur primaire phosphate (Zone A)
- CONVOYEUR-002 : Convoyeur principal (Zone B)
- POMPE-003 : Pompe centrifuge eau process (Zone C)
- MOTEUR-004 : Moteur électrique 500kW (Zone A)
- TAMIS-005 : Tamis vibrant classification (Zone B)
- COMPRESSEUR-006 : Compresseur air comprimé (Zone D)
- FILTRE-007 : Filtre-presse déshydratation (Zone C)
- VENTILATEUR-008 : Ventilateur extraction (Zone A)

**PERSONNEL :**
- Jean DUPONT : Technicien maintenance (Équipe A)
- Marie MARTIN : Superviseur maintenance (Management)
- Pierre DURAND : Opérateur production (Équipe B)
- Sophie BERNARD : Technicienne électrique (Équipe A)
- Luc MOREAU : Chef d'équipe (Équipe B)
- Anne PETIT : Magasinière pièces (Logistique)

**TÂCHES :**
- Maintenance préventive broyeur BROYEUR-001
- Réparation fuite pompe POMPE-003
- Inspection réglementaire TRANSFORMATEUR-010
- Changement courroie CONVOYEUR-002
- Nettoyage filtre FILTRE-007
- Calibrage instruments TAMIS-005

**PIÈCES :**
- ROULEMENT-6308 : Roulement à billes Ø40mm
- COURROIE-A120 : Courroie trapézoïdale A120
- JOINT-VITON-50 : Joint torique Viton Ø50mm
- FILTRE-HUILE-HF35 : Filtre hydraulique HF35

## 🔧 SPÉCIFICATIONS TECHNIQUES

**Architecture :**
```python
# Structure recommandée
sotramine_phosphate/
├── main.py                 # Point d'entrée
├── models/                 # Modèles de données
├── views/                  # Interfaces utilisateur
├── controllers/            # Logique métier
├── utils/                  # Utilitaires
└── resources/              # Ressources (icons, styles)
```

**Fonctionnalités requises :**
- Interface responsive (1366x768 minimum)
- Temps réel (KPIs, heure, alertes)
- Recherche et filtres avancés
- Export PDF/Excel
- Gestion d'erreurs robuste
- Performance optimisée
- Code modulaire et documenté

## 🎯 ÉTAPES DE DÉVELOPPEMENT

**PHASE 1 - FONDATIONS (30 min) :**
1. Structure projet et imports
2. Classe principale avec interface de base
3. Sidebar avec menu coloré et navigation
4. Zone de contenu avec système de modules
5. Thème et styles CSS professionnels

**PHASE 2 - TABLEAU DE BORD (45 min) :**
1. Page d'accueil avec titre et heure temps réel
2. 8 KPIs colorés avec valeurs réalistes
3. Graphiques de production et maintenance
4. Panneau d'alertes avec 4 niveaux
5. Actions rapides vers modules

**PHASE 3 - MODULE TÂCHES (60 min) :**
1. Table des tâches avec données d'exemple
2. Formulaire création/modification complet
3. Gestion statuts et priorités
4. Filtres et recherche
5. Assignation techniciens

**PHASE 4 - AUTRES MODULES (90 min) :**
1. Module équipements avec fiches
2. Module personnel et pointage
3. Module pièces avec gestion stock
4. Centre maintenance
5. Rapports et exports

**PHASE 5 - FINITIONS (30 min) :**
1. Base de données avec données d'exemple
2. Gestion d'erreurs
3. Optimisations performance
4. Tests et corrections
5. Documentation

## ✅ CRITÈRES DE QUALITÉ
- **Fonctionnel :** Application qui se lance sans erreur
- **Complet :** Tous les modules accessibles et informatifs
- **Moderne :** Interface professionnelle avec couleurs cohérentes
- **Réaliste :** Données d'exemple crédibles pour l'industrie
- **Performant :** Réactif et stable
- **Maintenable :** Code propre et documenté

## 🎯 RÉSULTAT ATTENDU
Une application desktop complète, moderne et professionnelle pour la gestion de maintenance industrielle, avec :
- Interface intuitive pour techniciens
- Tableaux de bord avec KPIs temps réel
- Gestion complète des tâches de maintenance
- Base de données avec données réalistes
- Navigation fluide entre tous les modules
- Design professionnel adapté à l'industrie
- Performance optimisée pour usage intensif

Commence par créer l'architecture de base avec l'interface principale, puis développe le tableau de bord avec KPIs, et ensuite le module de gestion des tâches complet. Assure-toi que l'application soit fonctionnelle à chaque étape et utilise des données d'exemple réalistes pour l'industrie du phosphate.

L'objectif est de créer l'application de gestion de maintenance industrielle la plus complète et professionnelle possible !
```

---

## 📋 INSTRUCTIONS D'UTILISATION

### 🎯 **COMMENT UTILISER CE PROMPT**

1. **Copie le prompt complet** ci-dessus (entre les ````)
2. **Colle-le dans un agent AI** de développement (Claude, GPT-4, etc.)
3. **Lance la conversation** et laisse l'agent développer
4. **Supervise le développement** étape par étape
5. **Teste l'application** à chaque phase importante

### 🔧 **CONSEILS POUR OPTIMISER LES RÉSULTATS**

**Pendant le développement :**
- ✅ **Demande des tests** à chaque étape importante
- ✅ **Vérifie la fonctionnalité** avant de passer à l'étape suivante
- ✅ **Demande des corrections** si quelque chose ne fonctionne pas
- ✅ **Insiste sur les données réalistes** pour l'industrie
- ✅ **Vérifie la cohérence visuelle** du design

**Si l'agent rencontre des difficultés :**
- 🔄 **Simplifie la demande** en demandant une étape à la fois
- 🔄 **Demande une version basique** puis enrichis progressivement
- 🔄 **Insiste sur la fonctionnalité** avant l'esthétique
- 🔄 **Demande des tests** fréquents du code généré

### 📊 **VALIDATION DU RÉSULTAT**

**L'application finale doit avoir :**
- ✅ **Interface qui se lance** sans erreur
- ✅ **Navigation fonctionnelle** entre tous les modules
- ✅ **Tableau de bord** avec KPIs temps réel
- ✅ **Module tâches** complet avec données
- ✅ **Design professionnel** cohérent
- ✅ **Données d'exemple** réalistes
- ✅ **Performance** acceptable (< 5s démarrage)
- ✅ **Code** propre et documenté

### 🎯 **RÉSULTAT ATTENDU**

Une application **complète**, **moderne** et **professionnelle** prête pour utilisation immédiate en environnement industriel, avec toutes les fonctionnalités de gestion de maintenance requises pour SOTRAMINE PHOSPHATE.

---

**🚀 PRÊT À LANCER LE DÉVELOPPEMENT AVEC UN AGENT AI !**
